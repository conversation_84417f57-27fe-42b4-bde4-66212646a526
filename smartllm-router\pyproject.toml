[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "smartllm-router"
dynamic = ["version"]
description = "Intelligent cost optimization for LLM APIs - Cut your AI costs by 98%"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "SmartLLM Router Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
keywords = ["llm", "ai", "cost-optimization", "openai", "anthropic", "router"]
requires-python = ">=3.8"

[project.urls]
Homepage = "https://github.com/juyterman1000/smartllm-router"
Repository = "https://github.com/juyterman1000/smartllm-router"
Documentation = "https://github.com/juyterman1000/smartllm-router/blob/main/README.md"
"Bug Tracker" = "https://github.com/juyterman1000/smartllm-router/issues"

[tool.setuptools.dynamic]
version = {attr = "smartllm_router.__version__"}

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["smartllm_router"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--verbose",
    "--tb=short",
    "--strict-markers",
]
markers = [
    "slow: marks tests as slow",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]
