---
name: 🐛 Bug Report
about: Create a report to help us improve SmartLLM Router
title: '[BUG] '
labels: ['bug', 'needs-triage']
assignees: ''
---

## 🐛 Bug Description

**A clear and concise description of what the bug is.**

## 🔄 Steps to Reproduce

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ Expected Behavior

**A clear and concise description of what you expected to happen.**

## ❌ Actual Behavior

**A clear and concise description of what actually happened.**

## 📸 Screenshots

**If applicable, add screenshots to help explain your problem.**

## 🖥️ Environment

**Please complete the following information:**

- **OS**: [e.g. Windows 10, macOS 12.0, Ubuntu 20.04]
- **Python Version**: [e.g. 3.9.7]
- **SmartLLM Router Version**: [e.g. 0.1.0]
- **Provider(s)**: [e.g. OpenAI, Anthropic, Google]

## 📋 Configuration

**Please share your router configuration (remove sensitive data like API keys):**

```python
router = SmartRouter(
    strategy="balanced",  # your configuration here
    # ... other settings
)
```

## 📝 Error Logs

**If applicable, add error logs or stack traces:**

```
Paste error logs here
```

## 🔍 Additional Context

**Add any other context about the problem here.**

## ✅ Checklist

- [ ] I have searched for existing issues
- [ ] I have provided all requested information
- [ ] I have removed sensitive data (API keys, etc.)
- [ ] I can reproduce this issue consistently

## 🤝 Contribution

- [ ] I would like to work on fixing this bug
- [ ] I need help understanding the codebase
- [ ] This is blocking my project
