"""
Quick test script to verify SmartLLM Router installation
"""

print("🧪 Testing SmartLLM Router installation...\n")

# Test 1: Import modules
try:
    from smartllm_router import SmartRouter, QueryAnalyzer, ModelSelector
    print("✅ Successfully imported SmartLLM Router modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    exit(1)

# Test 2: Create router instance
try:
    router = SmartRouter(strategy="balanced")
    print("✅ Successfully created router instance")
except Exception as e:
    print(f"❌ Failed to create router: {e}")
    exit(1)

# Test 3: Analyze a query
try:
    analyzer = QueryAnalyzer()
    complexity = analyzer.analyze("What is machine learning?")
    print(f"✅ Query analysis working - Complexity: {complexity.complexity_score:.2f}")
except Exception as e:
    print(f"❌ Failed to analyze query: {e}")
    exit(1)

# Test 4: Select a model
try:
    selector = ModelSelector()
    model = selector.select_model(complexity, strategy="balanced")
    print(f"✅ Model selection working - Selected: {model.name}")
except Exception as e:
    print(f"❌ Failed to select model: {e}")
    exit(1)

# Test 5: Check CLI is accessible
try:
    import smartllm_router.cli
    print("✅ CLI module is accessible")
except ImportError:
    print("⚠️  CLI module not found - you may need to create cli.py")

print("\n✨ Installation test complete!")
print("\nNext steps:")
print("1. Add your API keys to enable actual API calls")
print("2. Run: python examples/basic_usage.py")
print("3. Run: pytest tests/ (if you have tests)")
print("4. Try CLI: python -m smartllm_router.cli --help")