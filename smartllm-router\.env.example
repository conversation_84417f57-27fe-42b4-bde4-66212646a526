# SmartLLM Router Configuration
# Copy this file to .env and add your actual API keys

# OpenAI API Key (Required)
# Get your key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your-openai-api-key-here

# Anthropic API Key (Optional)
# Get your key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Google AI API Key (Optional)
# Get your key from: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your-google-api-key-here

# Mistral API Key (Optional)
# Get your key from: https://console.mistral.ai/
MISTRAL_API_KEY=your-mistral-api-key-here

# Router Configuration
SMARTLLM_STRATEGY=balanced
SMARTLLM_DAILY_BUDGET=100.0
SMARTLLM_CACHE_TTL=3600
SMARTLLM_ENABLE_FALLBACK=true
