# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ===== SECURITY - NEVER COMMIT THESE =====
# Environment variables and secrets
.env
.env.local
.env.production
.env.staging
*.key
*.pem
secrets.json
config.json

# API Keys (IMPORTANT: Never commit API keys!)
*api_key*
*API_KEY*
*secret*
*SECRET*
*token*
*TOKEN*
sk-*
sk-ant-*

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.idea/
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# ===== PROJECT SPECIFIC =====
# Test outputs and results
test_results/
benchmark_results/
performance_results/
*.test.log
benchmark_results.png
benchmark_report.json

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Cache files
.cache/
*.cache

# Log files
*.log
logs/

# Database files
*.db
*.sqlite
*.sqlite3

# Streamlit
.streamlit/

# Docker
docker-compose.override.yml

# Deployment
.vercel
.netlify
.railway
